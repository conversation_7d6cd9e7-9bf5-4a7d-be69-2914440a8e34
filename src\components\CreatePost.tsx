'use client';

import { useState } from 'react';
import { X, Image, Link, Type, Poll } from 'lucide-react';

interface CreatePostProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (post: {
    title: string;
    content: string;
    community: string;
    type: 'text' | 'image' | 'link';
  }) => void;
}

export default function CreatePost({ isOpen, onClose, onSubmit }: CreatePostProps) {
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [community, setCommunity] = useState('');
  const [postType, setPostType] = useState<'text' | 'image' | 'link'>('text');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (title.trim() && content.trim() && community.trim()) {
      onSubmit({
        title: title.trim(),
        content: content.trim(),
        community: community.trim(),
        type: postType
      });
      // Reset form
      setTitle('');
      setContent('');
      setCommunity('');
      setPostType('text');
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="hand-drawn-card bg-background max-w-2xl w-full max-h-[90vh] overflow-y-auto transform rotate-1">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b-2 border-border">
          <h2 className="text-2xl font-bold doodle-font">Create a Post ✏️</h2>
          <button
            onClick={onClose}
            className="hand-drawn-button p-2 hover:bg-muted"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Community Selection */}
          <div>
            <label className="block text-sm font-medium doodle-font mb-2">
              Choose a community
            </label>
            <select
              value={community}
              onChange={(e) => setCommunity(e.target.value)}
              className="sketchy-input w-full px-3 py-2 doodle-font"
              required
            >
              <option value="">Select a community...</option>
              <option value="r/funny">r/funny</option>
              <option value="r/AskReddit">r/AskReddit</option>
              <option value="r/gaming">r/gaming</option>
              <option value="r/aww">r/aww</option>
              <option value="r/pics">r/pics</option>
              <option value="r/science">r/science</option>
              <option value="r/mildlyinteresting">r/mildlyinteresting</option>
              <option value="r/LifeProTips">r/LifeProTips</option>
              <option value="r/wholesome">r/wholesome</option>
              <option value="r/Showerthoughts">r/Showerthoughts</option>
            </select>
          </div>

          {/* Post Type Selection */}
          <div>
            <label className="block text-sm font-medium doodle-font mb-2">
              Post type
            </label>
            <div className="flex space-x-2">
              <button
                type="button"
                onClick={() => setPostType('text')}
                className={`hand-drawn-button px-4 py-2 text-sm doodle-font ${
                  postType === 'text' ? 'bg-primary text-white' : 'hover:bg-muted'
                }`}
              >
                <Type className="h-4 w-4 mr-1" />
                Text
              </button>
              <button
                type="button"
                onClick={() => setPostType('image')}
                className={`hand-drawn-button px-4 py-2 text-sm doodle-font ${
                  postType === 'image' ? 'bg-primary text-white' : 'hover:bg-muted'
                }`}
              >
                <Image className="h-4 w-4 mr-1" />
                Image
              </button>
              <button
                type="button"
                onClick={() => setPostType('link')}
                className={`hand-drawn-button px-4 py-2 text-sm doodle-font ${
                  postType === 'link' ? 'bg-primary text-white' : 'hover:bg-muted'
                }`}
              >
                <Link className="h-4 w-4 mr-1" />
                Link
              </button>
            </div>
          </div>

          {/* Title */}
          <div>
            <label className="block text-sm font-medium doodle-font mb-2">
              Title
            </label>
            <input
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="An interesting title..."
              className="sketchy-input w-full px-3 py-2 doodle-font"
              maxLength={300}
              required
            />
            <p className="text-xs text-muted-foreground mt-1 doodle-font">
              {title.length}/300 characters
            </p>
          </div>

          {/* Content */}
          <div>
            <label className="block text-sm font-medium doodle-font mb-2">
              {postType === 'text' ? 'Text (optional)' : 
               postType === 'image' ? 'Image URL' : 'URL'}
            </label>
            <textarea
              value={content}
              onChange={(e) => setContent(e.target.value)}
              placeholder={
                postType === 'text' ? 'What are your thoughts?' :
                postType === 'image' ? 'https://example.com/image.jpg' :
                'https://example.com'
              }
              className="sketchy-input w-full px-3 py-2 doodle-font min-h-[120px] resize-y"
              required={postType !== 'text'}
            />
          </div>

          {/* Rules reminder */}
          <div className="hand-drawn-card p-4 bg-yellow-50 border-yellow-200 transform -rotate-1">
            <h3 className="font-bold doodle-font mb-2">📝 Remember the rules!</h3>
            <ul className="text-sm doodle-font space-y-1 text-muted-foreground">
              <li>• Be respectful and kind to others</li>
              <li>• No spam or self-promotion</li>
              <li>• Keep content relevant to the community</li>
              <li>• Use descriptive titles</li>
            </ul>
          </div>

          {/* Submit buttons */}
          <div className="flex space-x-4 pt-4">
            <button
              type="submit"
              className="hand-drawn-button px-6 py-3 bg-primary text-white doodle-font hover:bg-primary/90 flex-1"
            >
              Post to Sketchit! 🚀
            </button>
            <button
              type="button"
              onClick={onClose}
              className="hand-drawn-button px-6 py-3 doodle-font hover:bg-muted"
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
