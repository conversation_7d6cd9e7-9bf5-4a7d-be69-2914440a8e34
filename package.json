{"name": "reddit-clone", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@types/uuid": "^10.0.0", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "next": "15.4.1", "react": "19.1.0", "react-dom": "19.1.0", "roughjs": "^4.6.6", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.1", "tailwindcss": "^4", "typescript": "^5"}}