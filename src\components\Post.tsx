'use client';

import { ArrowUp, ArrowDown, MessageCircle, Share, Bookmark, MoreHorizontal } from 'lucide-react';
import { useState } from 'react';

interface PostProps {
  id: string;
  title: string;
  content: string;
  author: string;
  community: string;
  timeAgo: string;
  upvotes: number;
  downvotes: number;
  comments: number;
  imageUrl?: string;
  rotation?: number;
}

export default function Post({
  id,
  title,
  content,
  author,
  community,
  timeAgo,
  upvotes,
  downvotes,
  comments,
  imageUrl,
  rotation = 0
}: PostProps) {
  const [userVote, setUserVote] = useState<'up' | 'down' | null>(null);
  const [isSaved, setIsSaved] = useState(false);
  const [currentUpvotes, setCurrentUpvotes] = useState(upvotes);
  const [currentDownvotes, setCurrentDownvotes] = useState(downvotes);

  const handleVote = (voteType: 'up' | 'down') => {
    if (userVote === voteType) {
      // Remove vote
      setUserVote(null);
      if (voteType === 'up') {
        setCurrentUpvotes(prev => prev - 1);
      } else {
        setCurrentDownvotes(prev => prev - 1);
      }
    } else {
      // Change or add vote
      if (userVote) {
        // Changing vote
        if (userVote === 'up') {
          setCurrentUpvotes(prev => prev - 1);
          setCurrentDownvotes(prev => prev + 1);
        } else {
          setCurrentDownvotes(prev => prev - 1);
          setCurrentUpvotes(prev => prev + 1);
        }
      } else {
        // Adding new vote
        if (voteType === 'up') {
          setCurrentUpvotes(prev => prev + 1);
        } else {
          setCurrentDownvotes(prev => prev + 1);
        }
      }
      setUserVote(voteType);
    }
  };

  const totalScore = currentUpvotes - currentDownvotes;

  return (
    <article 
      className="hand-drawn-card p-6 hover:shadow-lg transition-all duration-200"
      style={{ transform: `rotate(${rotation}deg)` }}
    >
      <div className="flex items-start space-x-4">
        {/* Voting Section */}
        <div className="flex flex-col items-center space-y-2 min-w-[40px]">
          <button
            onClick={() => handleVote('up')}
            className={`hand-drawn-button p-2 transition-all duration-200 ${
              userVote === 'up' 
                ? 'bg-orange-500 text-white transform scale-110' 
                : 'text-orange-500 hover:bg-orange-50'
            }`}
          >
            <ArrowUp className="h-4 w-4" />
          </button>
          
          <span className={`font-bold doodle-font text-lg ${
            totalScore > 0 ? 'text-orange-500' : 
            totalScore < 0 ? 'text-blue-500' : 
            'text-gray-500'
          }`}>
            {totalScore}
          </span>
          
          <button
            onClick={() => handleVote('down')}
            className={`hand-drawn-button p-2 transition-all duration-200 ${
              userVote === 'down' 
                ? 'bg-blue-500 text-white transform scale-110' 
                : 'text-blue-500 hover:bg-blue-50'
            }`}
          >
            <ArrowDown className="h-4 w-4" />
          </button>
        </div>

        {/* Content Section */}
        <div className="flex-1 min-w-0">
          {/* Post Meta */}
          <div className="flex items-center space-x-2 mb-2 text-sm text-muted-foreground">
            <span className="doodle-font font-medium hover:underline cursor-pointer">
              {community}
            </span>
            <span>•</span>
            <span className="doodle-font">Posted by</span>
            <span className="doodle-font font-medium hover:underline cursor-pointer">
              u/{author}
            </span>
            <span>•</span>
            <span className="doodle-font">{timeAgo}</span>
          </div>

          {/* Post Title */}
          <h2 className="text-xl font-bold mb-3 doodle-font hover:text-primary cursor-pointer transition-colors">
            {title}
          </h2>

          {/* Post Content */}
          <div className="mb-4">
            {imageUrl && (
              <div className="mb-3">
                <img 
                  src={imageUrl} 
                  alt="Post content" 
                  className="max-w-full h-auto rounded-lg hand-drawn-border"
                />
              </div>
            )}
            <p className="text-muted-foreground doodle-font leading-relaxed">
              {content}
            </p>
          </div>

          {/* Post Actions */}
          <div className="flex items-center space-x-4">
            <button className="hand-drawn-button px-3 py-2 text-sm doodle-font hover:bg-muted transition-colors group">
              <MessageCircle className="h-4 w-4 mr-1 group-hover:wiggle" />
              {comments} comments
            </button>
            
            <button className="hand-drawn-button px-3 py-2 text-sm doodle-font hover:bg-muted transition-colors group">
              <Share className="h-4 w-4 mr-1 group-hover:wiggle" />
              Share
            </button>
            
            <button 
              onClick={() => setIsSaved(!isSaved)}
              className={`hand-drawn-button px-3 py-2 text-sm doodle-font transition-colors group ${
                isSaved ? 'bg-yellow-100 text-yellow-700' : 'hover:bg-muted'
              }`}
            >
              <Bookmark className={`h-4 w-4 mr-1 group-hover:wiggle ${isSaved ? 'fill-current' : ''}`} />
              {isSaved ? 'Saved' : 'Save'}
            </button>

            <button className="hand-drawn-button p-2 text-sm doodle-font hover:bg-muted transition-colors group">
              <MoreHorizontal className="h-4 w-4 group-hover:wiggle" />
            </button>
          </div>
        </div>
      </div>
    </article>
  );
}
