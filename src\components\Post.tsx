'use client';

import { ArrowUp, ArrowDown, MessageCircle, Share, Bookmark, MoreHorizontal, Award } from 'lucide-react';
import { useState } from 'react';

interface PostProps {
  id: string;
  title: string;
  content: string;
  author: string;
  community: string;
  timeAgo: string;
  upvotes: number;
  downvotes: number;
  comments: number;
  imageUrl?: string;
  awards?: number;
}

export default function Post({
  id,
  title,
  content,
  author,
  community,
  timeAgo,
  upvotes,
  downvotes,
  comments,
  imageUrl,
  awards = 0
}: PostProps) {
  const [userVote, setUserVote] = useState<'up' | 'down' | null>(null);
  const [isSaved, setIsSaved] = useState(false);
  const [currentUpvotes, setCurrentUpvotes] = useState(upvotes);
  const [currentDownvotes, setCurrentDownvotes] = useState(downvotes);

  const handleVote = (voteType: 'up' | 'down') => {
    if (userVote === voteType) {
      // Remove vote
      setUserVote(null);
      if (voteType === 'up') {
        setCurrentUpvotes(prev => prev - 1);
      } else {
        setCurrentDownvotes(prev => prev - 1);
      }
    } else {
      // Change or add vote
      if (userVote) {
        // Changing vote
        if (userVote === 'up') {
          setCurrentUpvotes(prev => prev - 1);
          setCurrentDownvotes(prev => prev + 1);
        } else {
          setCurrentDownvotes(prev => prev - 1);
          setCurrentUpvotes(prev => prev + 1);
        }
      } else {
        // Adding new vote
        if (voteType === 'up') {
          setCurrentUpvotes(prev => prev + 1);
        } else {
          setCurrentDownvotes(prev => prev + 1);
        }
      }
      setUserVote(voteType);
    }
  };

  const totalScore = currentUpvotes - currentDownvotes;
  const formatScore = (score: number) => {
    if (score >= 1000) {
      return `${(score / 1000).toFixed(1)}k`;
    }
    return score.toString();
  };

  return (
    <article className="reddit-card hover:border-border transition-colors">
      <div className="flex">
        {/* Voting Section */}
        <div className="flex flex-col items-center p-2 bg-muted/30 w-10">
          <button
            onClick={() => handleVote('up')}
            className={`vote-button ${userVote === 'up' ? 'upvoted' : ''}`}
          >
            <ArrowUp className="h-4 w-4" />
          </button>

          <span className={`text-xs font-bold py-1 ${
            userVote === 'up' ? 'text-upvote' :
            userVote === 'down' ? 'text-downvote' :
            'text-text-secondary'
          }`}>
            {formatScore(totalScore)}
          </span>

          <button
            onClick={() => handleVote('down')}
            className={`vote-button ${userVote === 'down' ? 'downvoted' : ''}`}
          >
            <ArrowDown className="h-4 w-4" />
          </button>
        </div>

        {/* Content Section */}
        <div className="flex-1 p-3">
          {/* Post Meta */}
          <div className="flex items-center space-x-1 mb-2 text-xs text-text-secondary">
            <span className="community-link text-foreground font-medium">
              {community}
            </span>
            <span>•</span>
            <span>Posted by</span>
            <span className="user-link">
              u/{author}
            </span>
            <span>•</span>
            <span>{timeAgo}</span>
            {awards > 0 && (
              <>
                <span>•</span>
                <div className="flex items-center space-x-1">
                  <Award className="h-3 w-3 text-yellow-500" />
                  <span>{awards}</span>
                </div>
              </>
            )}
          </div>

          {/* Post Title */}
          <h2 className="text-lg font-medium mb-2 text-foreground hover:text-accent cursor-pointer transition-colors leading-tight">
            {title}
          </h2>

          {/* Post Content */}
          <div className="mb-3">
            {imageUrl && (
              <div className="mb-3">
                <img
                  src={imageUrl}
                  alt="Post content"
                  className="max-w-full h-auto rounded border border-border-light"
                />
              </div>
            )}
            <p className="text-sm text-foreground leading-relaxed">
              {content}
            </p>
          </div>

          {/* Post Actions */}
          <div className="flex items-center space-x-2">
            <button className="post-action-button">
              <MessageCircle className="h-4 w-4 mr-1" />
              {comments} Comments
            </button>

            <button className="post-action-button">
              <Share className="h-4 w-4 mr-1" />
              Share
            </button>

            <button
              onClick={() => setIsSaved(!isSaved)}
              className={`post-action-button ${
                isSaved ? 'text-yellow-600' : ''
              }`}
            >
              <Bookmark className={`h-4 w-4 mr-1 ${isSaved ? 'fill-current' : ''}`} />
              Save
            </button>

            <button className="post-action-button">
              <MoreHorizontal className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>
    </article>
  );
}
