@import url('https://fonts.googleapis.com/css2?family=Kalam:wght@300;400;700&family=Caveat:wght@400;500;600;700&display=swap');
@import "tailwindcss";

:root {
  --background: #fefefe;
  --foreground: #2d3748;
  --primary: #4299e1;
  --secondary: #ed8936;
  --accent: #38b2ac;
  --muted: #f7fafc;
  --border: #e2e8f0;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-secondary: var(--secondary);
  --color-accent: var(--accent);
  --color-muted: var(--muted);
  --color-border: var(--border);
  --font-sans: 'Kalam', cursive;
  --font-mono: 'Caveat', cursive;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #1a202c;
    --foreground: #f7fafc;
    --primary: #63b3ed;
    --secondary: #fbb040;
    --accent: #4fd1c7;
    --muted: #2d3748;
    --border: #4a5568;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Kalam', cursive;
  background-image:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 200, 255, 0.03) 0%, transparent 50%);
}

/* Hand-drawn styling components */
.hand-drawn-border {
  position: relative;
  border: none;
}

.hand-drawn-border::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px solid currentColor;
  border-radius: 8px;
  transform: rotate(-0.5deg);
  z-index: -1;
}

.hand-drawn-border::after {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  border: 1px solid currentColor;
  border-radius: 6px;
  transform: rotate(0.3deg);
  z-index: -1;
}

.sketchy-shadow {
  filter: drop-shadow(3px 3px 0px rgba(0, 0, 0, 0.1))
          drop-shadow(2px 4px 0px rgba(0, 0, 0, 0.05));
}

.wiggle {
  animation: wiggle 2s ease-in-out infinite;
}

@keyframes wiggle {
  0%, 100% { transform: rotate(-1deg); }
  50% { transform: rotate(1deg); }
}

.hand-drawn-button {
  position: relative;
  background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
  transition: all 0.2s ease;
  transform: rotate(-0.2deg);
  border: 2px solid #2d3748;
  border-radius: 8px;
  filter: drop-shadow(3px 3px 0px rgba(0, 0, 0, 0.1));
}

.hand-drawn-button:hover {
  transform: rotate(0.2deg) scale(1.02);
  filter: drop-shadow(4px 4px 0px rgba(0, 0, 0, 0.15));
}

.hand-drawn-card {
  position: relative;
  background: #fefefe;
  transform: rotate(0.1deg);
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  filter: drop-shadow(3px 3px 0px rgba(0, 0, 0, 0.1));
}

.doodle-font {
  font-family: 'Caveat', cursive;
}

.sketchy-input {
  border: 2px solid #4a5568;
  border-radius: 8px;
  transform: rotate(-0.1deg);
  background: #fefefe;
  transition: all 0.2s ease;
}

.sketchy-input:focus {
  transform: rotate(0.1deg);
  border-color: #4299e1;
  outline: none;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
}
