@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import "tailwindcss";

:root {
  /* Reddit Light Theme Colors */
  --background: #ffffff;
  --foreground: #1c1c1c;
  --card-background: #ffffff;
  --border: #ccc;
  --border-light: #edeff1;
  --muted: #f6f7f8;
  --muted-foreground: #7c7c7c;
  --accent: #0079d3;
  --accent-hover: #0066cc;
  --upvote: #ff4500;
  --downvote: #7193ff;
  --text-secondary: #787c7e;
  --hover-bg: #f8f9fa;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card-background);
  --color-border: var(--border);
  --color-border-light: var(--border-light);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-hover: var(--accent-hover);
  --color-upvote: var(--upvote);
  --color-downvote: var(--downvote);
  --color-text-secondary: var(--text-secondary);
  --color-hover-bg: var(--hover-bg);
  --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #030303;
    --foreground: #d7dadc;
    --card-background: #1a1a1b;
    --border: #343536;
    --border-light: #474748;
    --muted: #272729;
    --muted-foreground: #818384;
    --accent: #0079d3;
    --accent-hover: #0066cc;
    --upvote: #ff4500;
    --downvote: #7193ff;
    --text-secondary: #818384;
    --hover-bg: #1a1a1b;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-size: 14px;
  line-height: 1.4;
}

/* Reddit-style components */
.reddit-button {
  @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-full transition-colors;
  @apply bg-transparent border border-border text-foreground hover:bg-hover-bg;
}

.reddit-button-primary {
  @apply reddit-button bg-accent text-white border-accent hover:bg-accent-hover;
}

.reddit-button-ghost {
  @apply reddit-button border-transparent hover:bg-hover-bg;
}

.reddit-card {
  @apply bg-card border border-border-light rounded-md;
}

.reddit-input {
  @apply w-full px-3 py-2 text-sm bg-background border border-border rounded-md;
  @apply focus:outline-none focus:ring-2 focus:ring-accent focus:border-transparent;
}

.reddit-search {
  @apply reddit-input bg-muted border-border-light;
}

.vote-button {
  @apply p-1 rounded-sm transition-colors;
}

.vote-button:hover {
  @apply bg-hover-bg;
}

.vote-button.upvoted {
  @apply text-upvote bg-upvote/10;
}

.vote-button.downvoted {
  @apply text-downvote bg-downvote/10;
}

.post-action-button {
  @apply inline-flex items-center px-2 py-1 text-xs font-medium text-text-secondary;
  @apply hover:bg-hover-bg rounded-sm transition-colors;
}

.community-link {
  @apply text-foreground hover:underline font-medium;
}

.user-link {
  @apply text-text-secondary hover:underline;
}

.reddit-sidebar-card {
  @apply reddit-card p-3 mb-4;
}

.reddit-nav-item {
  @apply flex items-center px-3 py-2 text-sm text-text-secondary hover:bg-hover-bg rounded-md transition-colors;
}

.reddit-nav-item.active {
  @apply text-foreground bg-hover-bg;
}
