'use client';

import { Search, Plus, User, Menu, MessageSquare, Bell, ChevronDown } from 'lucide-react';
import { useState } from 'react';
import CreatePost from './CreatePost';

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isCreatePostOpen, setIsCreatePostOpen] = useState(false);

  const handleCreatePost = (post: {
    title: string;
    content: string;
    community: string;
    type: 'text' | 'image' | 'link';
  }) => {
    // In a real app, this would send the post to a backend
    console.log('New post created:', post);
    // You could also add it to local state or trigger a refresh
  };

  return (
    <header className="sticky top-0 z-50 bg-background border-b border-border-light">
      <div className="flex items-center justify-between px-4 py-2 max-w-none">
        {/* Left section - Logo and nav */}
        <div className="flex items-center space-x-4">
          {/* Logo */}
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-accent rounded-full flex items-center justify-center">
              <span className="text-white font-bold text-sm">R</span>
            </div>
            <span className="hidden sm:block text-xl font-bold text-foreground">reddit</span>
          </div>

          {/* Navigation tabs */}
          <nav className="hidden lg:flex items-center space-x-1">
            <button className="reddit-nav-item active">
              <span>Home</span>
            </button>
            <button className="reddit-nav-item">
              <span>Popular</span>
            </button>
            <button className="reddit-nav-item">
              <span>All</span>
            </button>
          </nav>
        </div>

        {/* Center - Search */}
        <div className="flex-1 max-w-2xl mx-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <input
              type="text"
              placeholder="Search Reddit"
              className="reddit-search w-full pl-10 pr-4 py-2"
            />
          </div>
        </div>

        {/* Right section - Actions and user */}
        <div className="flex items-center space-x-2">
          {/* Action buttons */}
          <div className="hidden md:flex items-center space-x-1">
            <button className="reddit-button-ghost p-2">
              <MessageSquare className="h-5 w-5" />
            </button>
            <button className="reddit-button-ghost p-2">
              <Bell className="h-5 w-5" />
            </button>
            <button
              onClick={() => setIsCreatePostOpen(true)}
              className="reddit-button-primary"
            >
              <Plus className="h-4 w-4 mr-1" />
              Create
            </button>
          </div>

          {/* User menu */}
          <div className="flex items-center">
            <button className="reddit-button-ghost flex items-center space-x-2 px-2">
              <div className="w-6 h-6 bg-muted rounded-full flex items-center justify-center">
                <User className="h-4 w-4 text-muted-foreground" />
              </div>
              <span className="hidden sm:block text-sm">User</span>
              <ChevronDown className="h-4 w-4 text-muted-foreground" />
            </button>
          </div>

          {/* Mobile menu button */}
          <button
            className="md:hidden reddit-button-ghost p-2"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            <Menu className="h-5 w-5" />
          </button>
        </div>
      </div>

      {/* Mobile menu */}
      {isMenuOpen && (
        <div className="md:hidden border-t border-border-light bg-background">
          <div className="p-4 space-y-3">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <input
                type="text"
                placeholder="Search Reddit"
                className="reddit-search w-full pl-10 pr-4 py-2"
              />
            </div>
            <div className="space-y-1">
              <button className="reddit-nav-item w-full justify-start">Home</button>
              <button className="reddit-nav-item w-full justify-start">Popular</button>
              <button className="reddit-nav-item w-full justify-start">All</button>
              <button
                onClick={() => setIsCreatePostOpen(true)}
                className="reddit-button-primary w-full justify-center mt-3"
              >
                <Plus className="h-4 w-4 mr-1" />
                Create Post
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Create Post Modal */}
      <CreatePost
        isOpen={isCreatePostOpen}
        onClose={() => setIsCreatePostOpen(false)}
        onSubmit={handleCreatePost}
      />
    </header>
  );
}
