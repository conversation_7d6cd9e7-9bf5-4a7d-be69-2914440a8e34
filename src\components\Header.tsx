'use client';

import { Search, Plus, User, Menu } from 'lucide-react';
import { useState } from 'react';
import CreatePost from './CreatePost';

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isCreatePostOpen, setIsCreatePostOpen] = useState(false);

  const handleCreatePost = (post: {
    title: string;
    content: string;
    community: string;
    type: 'text' | 'image' | 'link';
  }) => {
    // In a real app, this would send the post to a backend
    console.log('New post created:', post);
    // You could also add it to local state or trigger a refresh
  };

  return (
    <header className="sticky top-0 z-50 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b-2 border-border">
      <div className="container mx-auto px-4 py-3">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <div className="flex items-center space-x-2">
            <div className="hand-drawn-card p-2 bg-primary text-white transform rotate-1">
              <h1 className="text-2xl font-bold doodle-font">Sketchit</h1>
            </div>
          </div>

          {/* Search Bar */}
          <div className="hidden md:flex flex-1 max-w-md mx-8">
            <div className="relative w-full">
              <input
                type="text"
                placeholder="Search communities and posts..."
                className="sketchy-input w-full px-4 py-2 pl-10 text-sm doodle-font placeholder:text-muted-foreground"
              />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            </div>
          </div>

          {/* Navigation */}
          <nav className="hidden md:flex items-center space-x-4">
            <button className="hand-drawn-button px-4 py-2 text-sm font-medium doodle-font hover:bg-muted">
              Popular
            </button>
            <button className="hand-drawn-button px-4 py-2 text-sm font-medium doodle-font hover:bg-muted">
              All
            </button>
            <button
              onClick={() => setIsCreatePostOpen(true)}
              className="hand-drawn-button px-4 py-2 text-sm font-medium doodle-font bg-primary text-white hover:bg-primary/90"
            >
              <Plus className="h-4 w-4 mr-1" />
              Create
            </button>
            <button className="hand-drawn-button p-2 hover:bg-muted">
              <User className="h-5 w-5" />
            </button>
          </nav>

          {/* Mobile Menu Button */}
          <button
            className="md:hidden hand-drawn-button p-2"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            <Menu className="h-5 w-5" />
          </button>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden mt-4 space-y-2">
            <div className="relative">
              <input
                type="text"
                placeholder="Search..."
                className="sketchy-input w-full px-4 py-2 pl-10 text-sm doodle-font"
              />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            </div>
            <div className="flex flex-col space-y-2">
              <button className="hand-drawn-button px-4 py-2 text-sm font-medium doodle-font text-left">
                Popular
              </button>
              <button className="hand-drawn-button px-4 py-2 text-sm font-medium doodle-font text-left">
                All
              </button>
              <button
                onClick={() => setIsCreatePostOpen(true)}
                className="hand-drawn-button px-4 py-2 text-sm font-medium doodle-font bg-primary text-white"
              >
                <Plus className="h-4 w-4 mr-1" />
                Create Post
              </button>
              <button className="hand-drawn-button px-4 py-2 text-sm font-medium doodle-font text-left">
                <User className="h-4 w-4 mr-1" />
                Profile
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Create Post Modal */}
      <CreatePost
        isOpen={isCreatePostOpen}
        onClose={() => setIsCreatePostOpen(false)}
        onSubmit={handleCreatePost}
      />
    </header>
  );
}
