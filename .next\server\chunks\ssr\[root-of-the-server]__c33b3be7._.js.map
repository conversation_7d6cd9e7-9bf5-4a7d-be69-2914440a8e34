{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Project/reddit-clone/src/components/Post.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Post.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Post.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2R,GACxT,yDACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Project/reddit-clone/src/components/Post.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Post.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Post.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAuQ,GACpS,qCACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Project/reddit-clone/src/data/samplePosts.ts"], "sourcesContent": ["export interface PostData {\n  id: string;\n  title: string;\n  content: string;\n  author: string;\n  community: string;\n  timeAgo: string;\n  upvotes: number;\n  downvotes: number;\n  comments: number;\n  imageUrl?: string;\n  rotation?: number;\n}\n\nexport const samplePosts: PostData[] = [\n  {\n    id: '1',\n    title: 'My cat discovered the printer and now thinks it\\'s a magic food dispenser 🐱',\n    content: 'Every time I print something, she sits there waiting for treats to come out. I don\\'t have the heart to tell her it doesn\\'t work that way... She even brings me her empty food bowl and places it next to the printer. I think she\\'s trying to show me where the food should come out.',\n    author: 'sketchyuser',\n    community: 'r/funny',\n    timeAgo: '2 hours ago',\n    upvotes: 45,\n    downvotes: 3,\n    comments: 23,\n    rotation: 1\n  },\n  {\n    id: '2',\n    title: 'What\\'s the weirdest thing you believed as a child? 🤔',\n    content: 'I used to think that if I swallowed a watermelon seed, a watermelon would grow in my stomach. I was terrified of eating watermelon for years! My mom finally convinced me it wasn\\'t true by showing me how plants need soil and sunlight.',\n    author: 'curious_mind',\n    community: 'r/AskReddit',\n    timeAgo: '4 hours ago',\n    upvotes: 167,\n    downvotes: 11,\n    comments: 89,\n    rotation: -1\n  },\n  {\n    id: '3',\n    title: 'Just finished my first indie game! It\\'s about a pencil that comes to life ✏️',\n    content: 'After 2 years of development, \"Sketchy Adventures\" is finally ready! It\\'s a hand-drawn platformer where you play as a magical pencil that can draw platforms and objects to solve puzzles. The art style matches this subreddit perfectly! Would love to hear your thoughts!',\n    author: 'pixel_artist',\n    community: 'r/gaming',\n    timeAgo: '6 hours ago',\n    upvotes: 78,\n    downvotes: 5,\n    comments: 45,\n    rotation: 0.5\n  },\n  {\n    id: '4',\n    title: 'Found this perfectly round rock at the beach today 🪨',\n    content: 'I\\'ve been collecting interesting rocks for years, but I\\'ve never found one this perfectly spherical! It\\'s about the size of a golf ball and completely smooth. Nature is amazing sometimes. My geology professor says it\\'s probably been tumbled by waves for decades.',\n    author: 'rock_collector',\n    community: 'r/mildlyinteresting',\n    timeAgo: '8 hours ago',\n    upvotes: 234,\n    downvotes: 12,\n    comments: 67,\n    rotation: -0.8\n  },\n  {\n    id: '5',\n    title: 'LPT: If you\\'re feeling overwhelmed, try the \"2-minute rule\" 💡',\n    content: 'If something takes less than 2 minutes to do, just do it immediately instead of putting it off. This has completely changed how I handle small tasks and reduced my stress levels significantly. Things like replying to texts, washing dishes, or filing papers.',\n    author: 'productivity_guru',\n    community: 'r/LifeProTips',\n    timeAgo: '12 hours ago',\n    upvotes: 892,\n    downvotes: 34,\n    comments: 156,\n    rotation: 1.2\n  },\n  {\n    id: '6',\n    title: 'My grandmother\\'s 95th birthday cake looked like a work of art 🎂',\n    content: 'She specifically requested a \"messy, hand-drawn\" style cake because she said \"perfect things are boring.\" The baker absolutely nailed it! It looks like something from a children\\'s book illustration. Grandma was so happy she cried.',\n    author: 'family_first',\n    community: 'r/wholesome',\n    timeAgo: '1 day ago',\n    upvotes: 1247,\n    downvotes: 18,\n    comments: 203,\n    rotation: -1.5\n  },\n  {\n    id: '7',\n    title: 'Shower thought: What if our pets think we\\'re really bad at being animals? 🚿',\n    content: 'Like, we can\\'t hunt, we need tools for everything, we wear weird coverings, and we stare at glowing rectangles all day. They probably think we\\'re the most helpless creatures ever but they love us anyway.',\n    author: 'deep_thinker',\n    community: 'r/Showerthoughts',\n    timeAgo: '1 day ago',\n    upvotes: 567,\n    downvotes: 23,\n    comments: 134,\n    rotation: 0.3\n  },\n  {\n    id: '8',\n    title: 'Made a hand-drawn map of my neighborhood for my D&D campaign 🗺️',\n    content: 'Spent the weekend turning my actual neighborhood into a fantasy setting. The grocery store is now a tavern, the park is an enchanted forest, and my neighbor\\'s garden gnomes are actual NPCs. My players love the familiar-yet-magical feel!',\n    author: 'dungeon_master',\n    community: 'r/DMAcademy',\n    timeAgo: '2 days ago',\n    upvotes: 445,\n    downvotes: 8,\n    comments: 78,\n    rotation: -0.7\n  }\n];\n"], "names": [], "mappings": ";;;AAcO,MAAM,cAA0B;IACrC;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,QAAQ;QACR,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,UAAU;QACV,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,QAAQ;QACR,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,UAAU;QACV,UAAU,CAAC;IACb;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,QAAQ;QACR,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,UAAU;QACV,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,QAAQ;QACR,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,UAAU;QACV,UAAU,CAAC;IACb;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,QAAQ;QACR,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,UAAU;QACV,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,QAAQ;QACR,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,UAAU;QACV,UAAU,CAAC;IACb;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,QAAQ;QACR,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,UAAU;QACV,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,QAAQ;QACR,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,UAAU;QACV,UAAU,CAAC;IACb;CACD", "debugId": null}}, {"offset": {"line": 155, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Project/reddit-clone/src/app/page.tsx"], "sourcesContent": ["import Post from '@/components/Post';\nimport { samplePosts } from '@/data/samplePosts';\n\nexport default function Home() {\n  return (\n    <div className=\"p-6 max-w-4xl mx-auto\">\n      {/* Welcome Message */}\n      <div className=\"hand-drawn-card p-6 mb-6 bg-gradient-to-r from-primary/10 to-secondary/10 transform -rotate-1\">\n        <h1 className=\"text-3xl font-bold doodle-font mb-2 transform rotate-1\">\n          Welcome to Sketchit! 🎨\n        </h1>\n        <p className=\"text-lg doodle-font text-muted-foreground\">\n          The hand-drawn Reddit clone where everything looks like it was sketched with love!\n        </p>\n      </div>\n\n      {/* Sort Options */}\n      <div className=\"flex space-x-4 mb-6\">\n        <button className=\"hand-drawn-button px-4 py-2 bg-primary text-white doodle-font\">\n          🔥 Hot\n        </button>\n        <button className=\"hand-drawn-button px-4 py-2 doodle-font hover:bg-muted\">\n          🆕 New\n        </button>\n        <button className=\"hand-drawn-button px-4 py-2 doodle-font hover:bg-muted\">\n          📈 Top\n        </button>\n        <button className=\"hand-drawn-button px-4 py-2 doodle-font hover:bg-muted\">\n          🌟 Best\n        </button>\n      </div>\n\n      {/* Posts */}\n      <div className=\"space-y-6\">\n        {samplePosts.map((post) => (\n          <Post\n            key={post.id}\n            {...post}\n          />\n        ))}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAyD;;;;;;kCAGvE,8OAAC;wBAAE,WAAU;kCAA4C;;;;;;;;;;;;0BAM3D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAO,WAAU;kCAAgE;;;;;;kCAGlF,8OAAC;wBAAO,WAAU;kCAAyD;;;;;;kCAG3E,8OAAC;wBAAO,WAAU;kCAAyD;;;;;;kCAG3E,8OAAC;wBAAO,WAAU;kCAAyD;;;;;;;;;;;;0BAM7E,8OAAC;gBAAI,WAAU;0BACZ,0HAAA,CAAA,cAAW,CAAC,GAAG,CAAC,CAAC,qBAChB,8OAAC,0HAAA,CAAA,UAAI;wBAEF,GAAG,IAAI;uBADH,KAAK,EAAE;;;;;;;;;;;;;;;;AAOxB", "debugId": null}}]}