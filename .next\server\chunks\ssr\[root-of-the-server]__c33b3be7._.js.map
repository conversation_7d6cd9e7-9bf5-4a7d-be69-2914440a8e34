{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Project/reddit-clone/src/components/Post.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Post.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Post.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2R,GACxT,yDACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Project/reddit-clone/src/components/Post.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Post.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Post.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAuQ,GACpS,qCACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Project/reddit-clone/src/data/samplePosts.ts"], "sourcesContent": ["export interface PostData {\n  id: string;\n  title: string;\n  content: string;\n  author: string;\n  community: string;\n  timeAgo: string;\n  upvotes: number;\n  downvotes: number;\n  comments: number;\n  imageUrl?: string;\n  awards?: number;\n}\n\nexport const samplePosts: PostData[] = [\n  {\n    id: '1',\n    title: 'My cat discovered the printer and now thinks it\\'s a magic food dispenser',\n    content: 'Every time I print something, she sits there waiting for treats to come out. I don\\'t have the heart to tell her it doesn\\'t work that way... She even brings me her empty food bowl and places it next to the printer. I think she\\'s trying to show me where the food should come out.',\n    author: 'catowner42',\n    community: 'r/funny',\n    timeAgo: '2h',\n    upvotes: 2847,\n    downvotes: 156,\n    comments: 234,\n    awards: 3\n  },\n  {\n    id: '2',\n    title: 'What\\'s the weirdest thing you believed as a child?',\n    content: 'I used to think that if I swallowed a watermelon seed, a watermelon would grow in my stomach. I was terrified of eating watermelon for years! My mom finally convinced me it wasn\\'t true by showing me how plants need soil and sunlight.',\n    author: 'nostalgic_redditor',\n    community: 'r/AskReddit',\n    timeAgo: '4h',\n    upvotes: 15672,\n    downvotes: 892,\n    comments: 1834,\n    awards: 12\n  },\n  {\n    id: '3',\n    title: 'Just finished my first indie game! It\\'s about a pencil that comes to life',\n    content: 'After 2 years of development, \"Sketchy Adventures\" is finally ready! It\\'s a hand-drawn platformer where you play as a magical pencil that can draw platforms and objects to solve puzzles. The art style matches this subreddit perfectly! Would love to hear your thoughts!',\n    author: 'indiedev_dreams',\n    community: 'r/gaming',\n    timeAgo: '6h',\n    upvotes: 8934,\n    downvotes: 234,\n    comments: 567,\n    awards: 8\n  },\n  {\n    id: '4',\n    title: 'Found this perfectly round rock at the beach today',\n    content: 'I\\'ve been collecting interesting rocks for years, but I\\'ve never found one this perfectly spherical! It\\'s about the size of a golf ball and completely smooth. Nature is amazing sometimes. My geology professor says it\\'s probably been tumbled by waves for decades.',\n    author: 'rock_hound_99',\n    community: 'r/mildlyinteresting',\n    timeAgo: '8h',\n    upvotes: 23456,\n    downvotes: 567,\n    comments: 892,\n    awards: 5\n  },\n  {\n    id: '5',\n    title: 'LPT: If you\\'re feeling overwhelmed, try the \"2-minute rule\"',\n    content: 'If something takes less than 2 minutes to do, just do it immediately instead of putting it off. This has completely changed how I handle small tasks and reduced my stress levels significantly. Things like replying to texts, washing dishes, or filing papers.',\n    author: 'productivity_ninja',\n    community: 'r/LifeProTips',\n    timeAgo: '12h',\n    upvotes: 45678,\n    downvotes: 1234,\n    comments: 2345,\n    awards: 15\n  },\n  {\n    id: '6',\n    title: 'My grandmother\\'s 95th birthday cake looked like a work of art',\n    content: 'She specifically requested a \"messy, hand-drawn\" style cake because she said \"perfect things are boring.\" The baker absolutely nailed it! It looks like something from a children\\'s book illustration. Grandma was so happy she cried.',\n    author: 'loving_grandchild',\n    community: 'r/wholesome',\n    timeAgo: '1d',\n    upvotes: 67890,\n    downvotes: 234,\n    comments: 3456,\n    awards: 25\n  },\n  {\n    id: '7',\n    title: 'Shower thought: What if our pets think we\\'re really bad at being animals?',\n    content: 'Like, we can\\'t hunt, we need tools for everything, we wear weird coverings, and we stare at glowing rectangles all day. They probably think we\\'re the most helpless creatures ever but they love us anyway.',\n    author: 'philosophical_pet_owner',\n    community: 'r/Showerthoughts',\n    timeAgo: '1d',\n    upvotes: 34567,\n    downvotes: 1234,\n    comments: 1789,\n    awards: 7\n  },\n  {\n    id: '8',\n    title: 'Made a hand-drawn map of my neighborhood for my D&D campaign',\n    content: 'Spent the weekend turning my actual neighborhood into a fantasy setting. The grocery store is now a tavern, the park is an enchanted forest, and my neighbor\\'s garden gnomes are actual NPCs. My players love the familiar-yet-magical feel!',\n    author: 'creative_dm',\n    community: 'r/DMAcademy',\n    timeAgo: '2d',\n    upvotes: 12345,\n    downvotes: 456,\n    comments: 678,\n    awards: 4\n  }\n];\n"], "names": [], "mappings": ";;;AAcO,MAAM,cAA0B;IACrC;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,QAAQ;QACR,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,UAAU;QACV,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,QAAQ;QACR,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,UAAU;QACV,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,QAAQ;QACR,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,UAAU;QACV,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,QAAQ;QACR,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,UAAU;QACV,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,QAAQ;QACR,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,UAAU;QACV,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,QAAQ;QACR,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,UAAU;QACV,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,QAAQ;QACR,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,UAAU;QACV,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,QAAQ;QACR,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,UAAU;QACV,QAAQ;IACV;CACD", "debugId": null}}, {"offset": {"line": 155, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Project/reddit-clone/src/app/page.tsx"], "sourcesContent": ["import Post from '@/components/Post';\nimport { samplePosts } from '@/data/samplePosts';\nimport { TrendingUp, Flame, Clock, BarChart3, ChevronDown } from 'lucide-react';\n\nexport default function Home() {\n  return (\n    <div className=\"flex-1 bg-muted/30\">\n      <div className=\"max-w-4xl mx-auto\">\n        {/* Sort Options */}\n        <div className=\"bg-background border-b border-border-light sticky top-16 z-10\">\n          <div className=\"flex items-center space-x-1 px-4 py-2\">\n            <button className=\"reddit-nav-item active\">\n              <Flame className=\"h-4 w-4 mr-2\" />\n              Hot\n            </button>\n            <button className=\"reddit-nav-item\">\n              <Clock className=\"h-4 w-4 mr-2\" />\n              New\n            </button>\n            <button className=\"reddit-nav-item\">\n              <BarChart3 className=\"h-4 w-4 mr-2\" />\n              Top\n            </button>\n            <button className=\"reddit-nav-item\">\n              <TrendingUp className=\"h-4 w-4 mr-2\" />\n              Rising\n            </button>\n            <div className=\"ml-auto\">\n              <button className=\"reddit-button-ghost flex items-center space-x-1\">\n                <span className=\"text-sm\">View</span>\n                <ChevronDown className=\"h-4 w-4\" />\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Posts */}\n        <div className=\"p-4 space-y-2\">\n          {samplePosts.map((post) => (\n            <Post\n              key={post.id}\n              {...post}\n            />\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAO,WAAU;;kDAChB,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGpC,8OAAC;gCAAO,WAAU;;kDAChB,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGpC,8OAAC;gCAAO,WAAU;;kDAChB,8OAAC,kNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGxC,8OAAC;gCAAO,WAAU;;kDAChB,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGzC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAO,WAAU;;sDAChB,8OAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAO/B,8OAAC;oBAAI,WAAU;8BACZ,0HAAA,CAAA,cAAW,CAAC,GAAG,CAAC,CAAC,qBAChB,8OAAC,0HAAA,CAAA,UAAI;4BAEF,GAAG,IAAI;2BADH,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;AAQ1B", "debugId": null}}]}