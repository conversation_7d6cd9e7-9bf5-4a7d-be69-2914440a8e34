{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Project/reddit-clone/src/app/page.tsx"], "sourcesContent": ["import Post from '@/components/Post';\nimport { samplePosts } from '@/data/samplePosts';\n\nexport default function Home() {\n  return (\n    <div className=\"p-6 max-w-4xl mx-auto\">\n      {/* Welcome Message */}\n      <div className=\"hand-drawn-card p-6 mb-6 bg-gradient-to-r from-primary/10 to-secondary/10 transform -rotate-1\">\n        <h1 className=\"text-3xl font-bold doodle-font mb-2 transform rotate-1\">\n          Welcome to Sketchit! 🎨\n        </h1>\n        <p className=\"text-lg doodle-font text-muted-foreground\">\n          The hand-drawn Reddit clone where everything looks like it was sketched with love!\n        </p>\n      </div>\n\n      {/* Sort Options */}\n      <div className=\"flex space-x-4 mb-6\">\n        <button className=\"hand-drawn-button px-4 py-2 bg-primary text-white doodle-font\">\n          🔥 Hot\n        </button>\n        <button className=\"hand-drawn-button px-4 py-2 doodle-font hover:bg-muted\">\n          🆕 New\n        </button>\n        <button className=\"hand-drawn-button px-4 py-2 doodle-font hover:bg-muted\">\n          📈 Top\n        </button>\n        <button className=\"hand-drawn-button px-4 py-2 doodle-font hover:bg-muted\">\n          🌟 Best\n        </button>\n      </div>\n\n      {/* Sample Posts */}\n      <div className=\"space-y-6\">\n        {/* Post 1 */}\n        <article className=\"hand-drawn-card p-6 transform rotate-1\">\n          <div className=\"flex items-start space-x-4\">\n            <div className=\"flex flex-col items-center space-y-2\">\n              <button className=\"hand-drawn-button p-2 text-orange-500 hover:bg-orange-50\">\n                ⬆️\n              </button>\n              <span className=\"font-bold doodle-font\">42</span>\n              <button className=\"hand-drawn-button p-2 text-blue-500 hover:bg-blue-50\">\n                ⬇️\n              </button>\n            </div>\n            <div className=\"flex-1\">\n              <div className=\"flex items-center space-x-2 mb-2\">\n                <span className=\"text-sm text-muted-foreground doodle-font\">r/funny</span>\n                <span className=\"text-sm text-muted-foreground\">•</span>\n                <span className=\"text-sm text-muted-foreground doodle-font\">Posted by u/sketchyuser</span>\n                <span className=\"text-sm text-muted-foreground\">•</span>\n                <span className=\"text-sm text-muted-foreground doodle-font\">2 hours ago</span>\n              </div>\n              <h2 className=\"text-xl font-bold mb-2 doodle-font\">\n                My cat discovered the printer and now thinks it's a magic food dispenser 🐱\n              </h2>\n              <p className=\"text-muted-foreground mb-4 doodle-font\">\n                Every time I print something, she sits there waiting for treats to come out.\n                I don't have the heart to tell her it doesn't work that way...\n              </p>\n              <div className=\"flex items-center space-x-4\">\n                <button className=\"hand-drawn-button px-3 py-1 text-sm doodle-font hover:bg-muted\">\n                  💬 23 comments\n                </button>\n                <button className=\"hand-drawn-button px-3 py-1 text-sm doodle-font hover:bg-muted\">\n                  📤 Share\n                </button>\n                <button className=\"hand-drawn-button px-3 py-1 text-sm doodle-font hover:bg-muted\">\n                  💾 Save\n                </button>\n              </div>\n            </div>\n          </div>\n        </article>\n\n        {/* Post 2 */}\n        <article className=\"hand-drawn-card p-6 transform -rotate-1\">\n          <div className=\"flex items-start space-x-4\">\n            <div className=\"flex flex-col items-center space-y-2\">\n              <button className=\"hand-drawn-button p-2 text-orange-500 hover:bg-orange-50\">\n                ⬆️\n              </button>\n              <span className=\"font-bold doodle-font\">156</span>\n              <button className=\"hand-drawn-button p-2 text-blue-500 hover:bg-blue-50\">\n                ⬇️\n              </button>\n            </div>\n            <div className=\"flex-1\">\n              <div className=\"flex items-center space-x-2 mb-2\">\n                <span className=\"text-sm text-muted-foreground doodle-font\">r/AskReddit</span>\n                <span className=\"text-sm text-muted-foreground\">•</span>\n                <span className=\"text-sm text-muted-foreground doodle-font\">Posted by u/curious_mind</span>\n                <span className=\"text-sm text-muted-foreground\">•</span>\n                <span className=\"text-sm text-muted-foreground doodle-font\">4 hours ago</span>\n              </div>\n              <h2 className=\"text-xl font-bold mb-2 doodle-font\">\n                What's the weirdest thing you believed as a child? 🤔\n              </h2>\n              <p className=\"text-muted-foreground mb-4 doodle-font\">\n                I used to think that if I swallowed a watermelon seed, a watermelon would grow in my stomach.\n                I was terrified of eating watermelon for years!\n              </p>\n              <div className=\"flex items-center space-x-4\">\n                <button className=\"hand-drawn-button px-3 py-1 text-sm doodle-font hover:bg-muted\">\n                  💬 89 comments\n                </button>\n                <button className=\"hand-drawn-button px-3 py-1 text-sm doodle-font hover:bg-muted\">\n                  📤 Share\n                </button>\n                <button className=\"hand-drawn-button px-3 py-1 text-sm doodle-font hover:bg-muted\">\n                  💾 Save\n                </button>\n              </div>\n            </div>\n          </div>\n        </article>\n\n        {/* Post 3 */}\n        <article className=\"hand-drawn-card p-6 transform rotate-1\">\n          <div className=\"flex items-start space-x-4\">\n            <div className=\"flex flex-col items-center space-y-2\">\n              <button className=\"hand-drawn-button p-2 text-orange-500 hover:bg-orange-50\">\n                ⬆️\n              </button>\n              <span className=\"font-bold doodle-font\">73</span>\n              <button className=\"hand-drawn-button p-2 text-blue-500 hover:bg-blue-50\">\n                ⬇️\n              </button>\n            </div>\n            <div className=\"flex-1\">\n              <div className=\"flex items-center space-x-2 mb-2\">\n                <span className=\"text-sm text-muted-foreground doodle-font\">r/gaming</span>\n                <span className=\"text-sm text-muted-foreground\">•</span>\n                <span className=\"text-sm text-muted-foreground doodle-font\">Posted by u/pixel_artist</span>\n                <span className=\"text-sm text-muted-foreground\">•</span>\n                <span className=\"text-sm text-muted-foreground doodle-font\">6 hours ago</span>\n              </div>\n              <h2 className=\"text-xl font-bold mb-2 doodle-font\">\n                Just finished my first indie game! It's about a pencil that comes to life ✏️\n              </h2>\n              <p className=\"text-muted-foreground mb-4 doodle-font\">\n                After 2 years of development, \"Sketchy Adventures\" is finally ready!\n                It's a hand-drawn platformer where you play as a magical pencil.\n                Would love to hear your thoughts!\n              </p>\n              <div className=\"flex items-center space-x-4\">\n                <button className=\"hand-drawn-button px-3 py-1 text-sm doodle-font hover:bg-muted\">\n                  💬 45 comments\n                </button>\n                <button className=\"hand-drawn-button px-3 py-1 text-sm doodle-font hover:bg-muted\">\n                  📤 Share\n                </button>\n                <button className=\"hand-drawn-button px-3 py-1 text-sm doodle-font hover:bg-muted\">\n                  💾 Save\n                </button>\n              </div>\n            </div>\n          </div>\n        </article>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAGe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAyD;;;;;;kCAGvE,8OAAC;wBAAE,WAAU;kCAA4C;;;;;;;;;;;;0BAM3D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAO,WAAU;kCAAgE;;;;;;kCAGlF,8OAAC;wBAAO,WAAU;kCAAyD;;;;;;kCAG3E,8OAAC;wBAAO,WAAU;kCAAyD;;;;;;kCAG3E,8OAAC;wBAAO,WAAU;kCAAyD;;;;;;;;;;;;0BAM7E,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAO,WAAU;sDAA2D;;;;;;sDAG7E,8OAAC;4CAAK,WAAU;sDAAwB;;;;;;sDACxC,8OAAC;4CAAO,WAAU;sDAAuD;;;;;;;;;;;;8CAI3E,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAA4C;;;;;;8DAC5D,8OAAC;oDAAK,WAAU;8DAAgC;;;;;;8DAChD,8OAAC;oDAAK,WAAU;8DAA4C;;;;;;8DAC5D,8OAAC;oDAAK,WAAU;8DAAgC;;;;;;8DAChD,8OAAC;oDAAK,WAAU;8DAA4C;;;;;;;;;;;;sDAE9D,8OAAC;4CAAG,WAAU;sDAAqC;;;;;;sDAGnD,8OAAC;4CAAE,WAAU;sDAAyC;;;;;;sDAItD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAO,WAAU;8DAAiE;;;;;;8DAGnF,8OAAC;oDAAO,WAAU;8DAAiE;;;;;;8DAGnF,8OAAC;oDAAO,WAAU;8DAAiE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS3F,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAO,WAAU;sDAA2D;;;;;;sDAG7E,8OAAC;4CAAK,WAAU;sDAAwB;;;;;;sDACxC,8OAAC;4CAAO,WAAU;sDAAuD;;;;;;;;;;;;8CAI3E,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAA4C;;;;;;8DAC5D,8OAAC;oDAAK,WAAU;8DAAgC;;;;;;8DAChD,8OAAC;oDAAK,WAAU;8DAA4C;;;;;;8DAC5D,8OAAC;oDAAK,WAAU;8DAAgC;;;;;;8DAChD,8OAAC;oDAAK,WAAU;8DAA4C;;;;;;;;;;;;sDAE9D,8OAAC;4CAAG,WAAU;sDAAqC;;;;;;sDAGnD,8OAAC;4CAAE,WAAU;sDAAyC;;;;;;sDAItD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAO,WAAU;8DAAiE;;;;;;8DAGnF,8OAAC;oDAAO,WAAU;8DAAiE;;;;;;8DAGnF,8OAAC;oDAAO,WAAU;8DAAiE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS3F,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAO,WAAU;sDAA2D;;;;;;sDAG7E,8OAAC;4CAAK,WAAU;sDAAwB;;;;;;sDACxC,8OAAC;4CAAO,WAAU;sDAAuD;;;;;;;;;;;;8CAI3E,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAA4C;;;;;;8DAC5D,8OAAC;oDAAK,WAAU;8DAAgC;;;;;;8DAChD,8OAAC;oDAAK,WAAU;8DAA4C;;;;;;8DAC5D,8OAAC;oDAAK,WAAU;8DAAgC;;;;;;8DAChD,8OAAC;oDAAK,WAAU;8DAA4C;;;;;;;;;;;;sDAE9D,8OAAC;4CAAG,WAAU;sDAAqC;;;;;;sDAGnD,8OAAC;4CAAE,WAAU;sDAAyC;;;;;;sDAKtD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAO,WAAU;8DAAiE;;;;;;8DAGnF,8OAAC;oDAAO,WAAU;8DAAiE;;;;;;8DAGnF,8OAAC;oDAAO,WAAU;8DAAiE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUnG", "debugId": null}}]}