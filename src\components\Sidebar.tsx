'use client';

import { Home, TrendingUp, Users, Bookmark, Settings, Plus, ChevronDown } from 'lucide-react';

const communities = [
  { name: 'r/funny', members: '42.1M', avatar: 'https://styles.redditmedia.com/t5_2qh33/styles/communityIcon_tajjpjp9flq61.png' },
  { name: 'r/AskReddit', members: '35.2M', avatar: 'https://styles.redditmedia.com/t5_2qh1i/styles/communityIcon_tijjpjp9flq61.png' },
  { name: 'r/gaming', members: '31.8M', avatar: 'https://styles.redditmedia.com/t5_2qh03/styles/communityIcon_tijjpjp9flq61.png' },
  { name: 'r/aww', members: '29.4M', avatar: 'https://styles.redditmedia.com/t5_2qh1o/styles/communityIcon_tijjpjp9flq61.png' },
  { name: 'r/pics', members: '28.7M', avatar: 'https://styles.redditmedia.com/t5_2qh0u/styles/communityIcon_tijjpjp9flq61.png' },
];

export default function Sidebar() {
  return (
    <aside className="w-80 min-h-screen bg-background border-r border-border-light">
      <div className="p-4 space-y-4">
        {/* Home section */}
        <div className="reddit-sidebar-card">
          <div className="flex items-center space-x-3 mb-3">
            <div className="w-8 h-8 bg-accent rounded-full flex items-center justify-center">
              <Home className="h-4 w-4 text-white" />
            </div>
            <span className="font-medium">Home</span>
          </div>
          <p className="text-sm text-text-secondary mb-3">
            Your personal Reddit frontpage. Come here to check in with your favorite communities.
          </p>
          <button className="reddit-button-primary w-full">
            Create Post
          </button>
          <button className="reddit-button w-full mt-2">
            Create Community
          </button>
        </div>

        {/* Recent communities */}
        <div className="reddit-sidebar-card">
          <div className="flex items-center justify-between mb-3">
            <span className="text-xs font-medium text-text-secondary uppercase tracking-wide">Recent</span>
            <ChevronDown className="h-4 w-4 text-text-secondary" />
          </div>
          <div className="space-y-1">
            <a href="#" className="reddit-nav-item">
              <div className="w-5 h-5 bg-accent rounded-full flex items-center justify-center mr-2">
                <span className="text-white text-xs">r/</span>
              </div>
              <span className="text-sm">reactjs</span>
            </a>
            <a href="#" className="reddit-nav-item">
              <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center mr-2">
                <span className="text-white text-xs">r/</span>
              </div>
              <span className="text-sm">webdev</span>
            </a>
            <a href="#" className="reddit-nav-item">
              <div className="w-5 h-5 bg-purple-500 rounded-full flex items-center justify-center mr-2">
                <span className="text-white text-xs">r/</span>
              </div>
              <span className="text-sm">programming</span>
            </a>
          </div>
        </div>

        {/* Communities */}
        <div className="reddit-sidebar-card">
          <div className="flex items-center justify-between mb-3">
            <span className="text-xs font-medium text-text-secondary uppercase tracking-wide">Communities</span>
            <ChevronDown className="h-4 w-4 text-text-secondary" />
          </div>
          <div className="space-y-1">
            {communities.map((community) => (
              <a
                key={community.name}
                href="#"
                className="reddit-nav-item"
              >
                <div className="w-5 h-5 bg-muted rounded-full flex items-center justify-center mr-2">
                  <span className="text-xs">r/</span>
                </div>
                <div className="flex-1 min-w-0">
                  <div className="text-sm truncate">{community.name}</div>
                </div>
              </a>
            ))}
          </div>
          <button className="reddit-nav-item w-full justify-start mt-2 text-accent">
            <Plus className="h-4 w-4 mr-2" />
            <span className="text-sm">Create Community</span>
          </button>
        </div>

        {/* Resources */}
        <div className="reddit-sidebar-card">
          <div className="flex items-center justify-between mb-3">
            <span className="text-xs font-medium text-text-secondary uppercase tracking-wide">Resources</span>
            <ChevronDown className="h-4 w-4 text-text-secondary" />
          </div>
          <div className="space-y-1">
            <a href="#" className="reddit-nav-item">
              <span className="text-sm">About Reddit</span>
            </a>
            <a href="#" className="reddit-nav-item">
              <span className="text-sm">Advertise</span>
            </a>
            <a href="#" className="reddit-nav-item">
              <span className="text-sm">Help</span>
            </a>
            <a href="#" className="reddit-nav-item">
              <span className="text-sm">Blog</span>
            </a>
            <a href="#" className="reddit-nav-item">
              <span className="text-sm">Careers</span>
            </a>
            <a href="#" className="reddit-nav-item">
              <span className="text-sm">Press</span>
            </a>
          </div>
        </div>

        {/* Footer */}
        <div className="text-xs text-text-secondary space-y-1 px-3">
          <div className="flex flex-wrap gap-x-3 gap-y-1">
            <a href="#" className="hover:underline">User Agreement</a>
            <a href="#" className="hover:underline">Privacy Policy</a>
            <a href="#" className="hover:underline">Content Policy</a>
            <a href="#" className="hover:underline">Moderator Code of Conduct</a>
          </div>
          <div className="pt-2">
            Reddit, Inc. © 2024. All rights reserved.
          </div>
        </div>
      </div>
    </aside>
  );
}
