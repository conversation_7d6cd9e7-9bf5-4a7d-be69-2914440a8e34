'use client';

import { Home, TrendingUp, Users, Bookmark, Setting<PERSON>, Star } from 'lucide-react';

const communities = [
  { name: 'r/funny', members: '42.1M', icon: '😂' },
  { name: 'r/AskReddit', members: '35.2M', icon: '❓' },
  { name: 'r/gaming', members: '31.8M', icon: '🎮' },
  { name: 'r/aww', members: '29.4M', icon: '🐱' },
  { name: 'r/pics', members: '28.7M', icon: '📸' },
  { name: 'r/science', members: '26.1M', icon: '🔬' },
];

export default function Sidebar() {
  return (
    <aside className="w-64 h-screen sticky top-16 overflow-y-auto p-4 space-y-6">
      {/* Main Navigation */}
      <div className="hand-drawn-card p-4 space-y-3">
        <h2 className="text-lg font-bold doodle-font mb-4 transform -rotate-1">Navigation</h2>
        <nav className="space-y-2">
          <a href="#" className="flex items-center space-x-3 p-2 rounded-lg hover:bg-muted transition-colors group">
            <Home className="h-5 w-5 text-primary group-hover:wiggle" />
            <span className="font-medium doodle-font">Home</span>
          </a>
          <a href="#" className="flex items-center space-x-3 p-2 rounded-lg hover:bg-muted transition-colors group">
            <TrendingUp className="h-5 w-5 text-secondary group-hover:wiggle" />
            <span className="font-medium doodle-font">Popular</span>
          </a>
          <a href="#" className="flex items-center space-x-3 p-2 rounded-lg hover:bg-muted transition-colors group">
            <Users className="h-5 w-5 text-accent group-hover:wiggle" />
            <span className="font-medium doodle-font">Communities</span>
          </a>
          <a href="#" className="flex items-center space-x-3 p-2 rounded-lg hover:bg-muted transition-colors group">
            <Bookmark className="h-5 w-5 text-purple-500 group-hover:wiggle" />
            <span className="font-medium doodle-font">Saved</span>
          </a>
          <a href="#" className="flex items-center space-x-3 p-2 rounded-lg hover:bg-muted transition-colors group">
            <Settings className="h-5 w-5 text-gray-500 group-hover:wiggle" />
            <span className="font-medium doodle-font">Settings</span>
          </a>
        </nav>
      </div>

      {/* Popular Communities */}
      <div className="hand-drawn-card p-4 space-y-3 transform rotate-1">
        <h2 className="text-lg font-bold doodle-font mb-4 flex items-center">
          <Star className="h-5 w-5 mr-2 text-yellow-500" />
          Popular Communities
        </h2>
        <div className="space-y-2">
          {communities.map((community, index) => (
            <a
              key={community.name}
              href="#"
              className="flex items-center space-x-3 p-2 rounded-lg hover:bg-muted transition-colors group"
              style={{ transform: `rotate(${(index % 2 === 0 ? 0.2 : -0.2)}deg)` }}
            >
              <span className="text-lg">{community.icon}</span>
              <div className="flex-1 min-w-0">
                <p className="font-medium doodle-font text-sm truncate">{community.name}</p>
                <p className="text-xs text-muted-foreground">{community.members} members</p>
              </div>
            </a>
          ))}
        </div>
        <button className="hand-drawn-button w-full py-2 text-sm doodle-font mt-4 hover:bg-muted">
          View All Communities
        </button>
      </div>

      {/* Create Community */}
      <div className="hand-drawn-card p-4 bg-gradient-to-br from-primary/10 to-secondary/10 transform -rotate-1">
        <h3 className="font-bold doodle-font mb-2">Create a Community</h3>
        <p className="text-sm text-muted-foreground mb-3 doodle-font">
          Build and grow a community about something you care about!
        </p>
        <button className="hand-drawn-button w-full py-2 text-sm doodle-font bg-primary text-white hover:bg-primary/90">
          Create Community
        </button>
      </div>
    </aside>
  );
}
