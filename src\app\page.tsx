import Post from '@/components/Post';
import { samplePosts } from '@/data/samplePosts';
import { TrendingUp, Flame, Clock, BarChart3, ChevronDown } from 'lucide-react';

export default function Home() {
  return (
    <div className="flex-1 bg-muted/30">
      <div className="max-w-4xl mx-auto">
        {/* Sort Options */}
        <div className="bg-background border-b border-border-light sticky top-16 z-10">
          <div className="flex items-center space-x-1 px-4 py-2">
            <button className="reddit-nav-item active">
              <Flame className="h-4 w-4 mr-2" />
              Hot
            </button>
            <button className="reddit-nav-item">
              <Clock className="h-4 w-4 mr-2" />
              New
            </button>
            <button className="reddit-nav-item">
              <BarChart3 className="h-4 w-4 mr-2" />
              Top
            </button>
            <button className="reddit-nav-item">
              <TrendingUp className="h-4 w-4 mr-2" />
              Rising
            </button>
            <div className="ml-auto">
              <button className="reddit-button-ghost flex items-center space-x-1">
                <span className="text-sm">View</span>
                <ChevronDown className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>

        {/* Posts */}
        <div className="p-4 space-y-2">
          {samplePosts.map((post) => (
            <Post
              key={post.id}
              {...post}
            />
          ))}
        </div>
      </div>
    </div>
  );
}
