import Post from '@/components/Post';
import { samplePosts } from '@/data/samplePosts';

export default function Home() {
  return (
    <div className="p-6 max-w-4xl mx-auto">
      {/* Welcome Message */}
      <div className="hand-drawn-card p-6 mb-6 bg-gradient-to-r from-primary/10 to-secondary/10 transform -rotate-1">
        <h1 className="text-3xl font-bold doodle-font mb-2 transform rotate-1">
          Welcome to Sketchit! 🎨
        </h1>
        <p className="text-lg doodle-font text-muted-foreground">
          The hand-drawn Reddit clone where everything looks like it was sketched with love!
        </p>
      </div>

      {/* Sort Options */}
      <div className="flex space-x-4 mb-6">
        <button className="hand-drawn-button px-4 py-2 bg-primary text-white doodle-font">
          🔥 Hot
        </button>
        <button className="hand-drawn-button px-4 py-2 doodle-font hover:bg-muted">
          🆕 New
        </button>
        <button className="hand-drawn-button px-4 py-2 doodle-font hover:bg-muted">
          📈 Top
        </button>
        <button className="hand-drawn-button px-4 py-2 doodle-font hover:bg-muted">
          🌟 Best
        </button>
      </div>

      {/* Posts */}
      <div className="space-y-6">
        {samplePosts.map((post) => (
          <Post
            key={post.id}
            {...post}
          />
        ))}
      </div>
    </div>
  );
}
