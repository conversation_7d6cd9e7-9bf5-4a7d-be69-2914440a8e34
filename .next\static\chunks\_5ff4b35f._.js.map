{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Project/reddit-clone/src/components/Post.tsx"], "sourcesContent": ["'use client';\n\nimport { ArrowUp, ArrowDown, MessageCircle, Share, Bookmark, MoreHorizontal } from 'lucide-react';\nimport { useState } from 'react';\n\ninterface PostProps {\n  id: string;\n  title: string;\n  content: string;\n  author: string;\n  community: string;\n  timeAgo: string;\n  upvotes: number;\n  downvotes: number;\n  comments: number;\n  imageUrl?: string;\n  rotation?: number;\n}\n\nexport default function Post({\n  id,\n  title,\n  content,\n  author,\n  community,\n  timeAgo,\n  upvotes,\n  downvotes,\n  comments,\n  imageUrl,\n  rotation = 0\n}: PostProps) {\n  const [userVote, setUserVote] = useState<'up' | 'down' | null>(null);\n  const [isSaved, setIsSaved] = useState(false);\n  const [currentUpvotes, setCurrentUpvotes] = useState(upvotes);\n  const [currentDownvotes, setCurrentDownvotes] = useState(downvotes);\n\n  const handleVote = (voteType: 'up' | 'down') => {\n    if (userVote === voteType) {\n      // Remove vote\n      setUserVote(null);\n      if (voteType === 'up') {\n        setCurrentUpvotes(prev => prev - 1);\n      } else {\n        setCurrentDownvotes(prev => prev - 1);\n      }\n    } else {\n      // Change or add vote\n      if (userVote) {\n        // Changing vote\n        if (userVote === 'up') {\n          setCurrentUpvotes(prev => prev - 1);\n          setCurrentDownvotes(prev => prev + 1);\n        } else {\n          setCurrentDownvotes(prev => prev - 1);\n          setCurrentUpvotes(prev => prev + 1);\n        }\n      } else {\n        // Adding new vote\n        if (voteType === 'up') {\n          setCurrentUpvotes(prev => prev + 1);\n        } else {\n          setCurrentDownvotes(prev => prev + 1);\n        }\n      }\n      setUserVote(voteType);\n    }\n  };\n\n  const totalScore = currentUpvotes - currentDownvotes;\n\n  return (\n    <article \n      className=\"hand-drawn-card p-6 hover:shadow-lg transition-all duration-200\"\n      style={{ transform: `rotate(${rotation}deg)` }}\n    >\n      <div className=\"flex items-start space-x-4\">\n        {/* Voting Section */}\n        <div className=\"flex flex-col items-center space-y-2 min-w-[40px]\">\n          <button\n            onClick={() => handleVote('up')}\n            className={`hand-drawn-button p-2 transition-all duration-200 ${\n              userVote === 'up' \n                ? 'bg-orange-500 text-white transform scale-110' \n                : 'text-orange-500 hover:bg-orange-50'\n            }`}\n          >\n            <ArrowUp className=\"h-4 w-4\" />\n          </button>\n          \n          <span className={`font-bold doodle-font text-lg ${\n            totalScore > 0 ? 'text-orange-500' : \n            totalScore < 0 ? 'text-blue-500' : \n            'text-gray-500'\n          }`}>\n            {totalScore}\n          </span>\n          \n          <button\n            onClick={() => handleVote('down')}\n            className={`hand-drawn-button p-2 transition-all duration-200 ${\n              userVote === 'down' \n                ? 'bg-blue-500 text-white transform scale-110' \n                : 'text-blue-500 hover:bg-blue-50'\n            }`}\n          >\n            <ArrowDown className=\"h-4 w-4\" />\n          </button>\n        </div>\n\n        {/* Content Section */}\n        <div className=\"flex-1 min-w-0\">\n          {/* Post Meta */}\n          <div className=\"flex items-center space-x-2 mb-2 text-sm text-muted-foreground\">\n            <span className=\"doodle-font font-medium hover:underline cursor-pointer\">\n              {community}\n            </span>\n            <span>•</span>\n            <span className=\"doodle-font\">Posted by</span>\n            <span className=\"doodle-font font-medium hover:underline cursor-pointer\">\n              u/{author}\n            </span>\n            <span>•</span>\n            <span className=\"doodle-font\">{timeAgo}</span>\n          </div>\n\n          {/* Post Title */}\n          <h2 className=\"text-xl font-bold mb-3 doodle-font hover:text-primary cursor-pointer transition-colors\">\n            {title}\n          </h2>\n\n          {/* Post Content */}\n          <div className=\"mb-4\">\n            {imageUrl && (\n              <div className=\"mb-3\">\n                <img \n                  src={imageUrl} \n                  alt=\"Post content\" \n                  className=\"max-w-full h-auto rounded-lg hand-drawn-border\"\n                />\n              </div>\n            )}\n            <p className=\"text-muted-foreground doodle-font leading-relaxed\">\n              {content}\n            </p>\n          </div>\n\n          {/* Post Actions */}\n          <div className=\"flex items-center space-x-4\">\n            <button className=\"hand-drawn-button px-3 py-2 text-sm doodle-font hover:bg-muted transition-colors group\">\n              <MessageCircle className=\"h-4 w-4 mr-1 group-hover:wiggle\" />\n              {comments} comments\n            </button>\n            \n            <button className=\"hand-drawn-button px-3 py-2 text-sm doodle-font hover:bg-muted transition-colors group\">\n              <Share className=\"h-4 w-4 mr-1 group-hover:wiggle\" />\n              Share\n            </button>\n            \n            <button \n              onClick={() => setIsSaved(!isSaved)}\n              className={`hand-drawn-button px-3 py-2 text-sm doodle-font transition-colors group ${\n                isSaved ? 'bg-yellow-100 text-yellow-700' : 'hover:bg-muted'\n              }`}\n            >\n              <Bookmark className={`h-4 w-4 mr-1 group-hover:wiggle ${isSaved ? 'fill-current' : ''}`} />\n              {isSaved ? 'Saved' : 'Save'}\n            </button>\n\n            <button className=\"hand-drawn-button p-2 text-sm doodle-font hover:bg-muted transition-colors group\">\n              <MoreHorizontal className=\"h-4 w-4 group-hover:wiggle\" />\n            </button>\n          </div>\n        </div>\n      </div>\n    </article>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAHA;;;AAmBe,SAAS,KAAK,KAYjB;QAZiB,EAC3B,EAAE,EACF,KAAK,EACL,OAAO,EACP,MAAM,EACN,SAAS,EACT,OAAO,EACP,OAAO,EACP,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,WAAW,CAAC,EACF,GAZiB;;IAa3B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IAC/D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,aAAa,CAAC;QAClB,IAAI,aAAa,UAAU;YACzB,cAAc;YACd,YAAY;YACZ,IAAI,aAAa,MAAM;gBACrB,kBAAkB,CAAA,OAAQ,OAAO;YACnC,OAAO;gBACL,oBAAoB,CAAA,OAAQ,OAAO;YACrC;QACF,OAAO;YACL,qBAAqB;YACrB,IAAI,UAAU;gBACZ,gBAAgB;gBAChB,IAAI,aAAa,MAAM;oBACrB,kBAAkB,CAAA,OAAQ,OAAO;oBACjC,oBAAoB,CAAA,OAAQ,OAAO;gBACrC,OAAO;oBACL,oBAAoB,CAAA,OAAQ,OAAO;oBACnC,kBAAkB,CAAA,OAAQ,OAAO;gBACnC;YACF,OAAO;gBACL,kBAAkB;gBAClB,IAAI,aAAa,MAAM;oBACrB,kBAAkB,CAAA,OAAQ,OAAO;gBACnC,OAAO;oBACL,oBAAoB,CAAA,OAAQ,OAAO;gBACrC;YACF;YACA,YAAY;QACd;IACF;IAEA,MAAM,aAAa,iBAAiB;IAEpC,qBACE,6LAAC;QACC,WAAU;QACV,OAAO;YAAE,WAAW,AAAC,UAAkB,OAAT,UAAS;QAAM;kBAE7C,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS,IAAM,WAAW;4BAC1B,WAAW,AAAC,qDAIX,OAHC,aAAa,OACT,iDACA;sCAGN,cAAA,6LAAC,+MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;;;;;;sCAGrB,6LAAC;4BAAK,WAAW,AAAC,iCAIjB,OAHC,aAAa,IAAI,oBACjB,aAAa,IAAI,kBACjB;sCAEC;;;;;;sCAGH,6LAAC;4BACC,SAAS,IAAM,WAAW;4BAC1B,WAAW,AAAC,qDAIX,OAHC,aAAa,SACT,+CACA;sCAGN,cAAA,6LAAC,mNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAKzB,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CACb;;;;;;8CAEH,6LAAC;8CAAK;;;;;;8CACN,6LAAC;oCAAK,WAAU;8CAAc;;;;;;8CAC9B,6LAAC;oCAAK,WAAU;;wCAAyD;wCACpE;;;;;;;8CAEL,6LAAC;8CAAK;;;;;;8CACN,6LAAC;oCAAK,WAAU;8CAAe;;;;;;;;;;;;sCAIjC,6LAAC;4BAAG,WAAU;sCACX;;;;;;sCAIH,6LAAC;4BAAI,WAAU;;gCACZ,0BACC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,KAAK;wCACL,KAAI;wCACJ,WAAU;;;;;;;;;;;8CAIhB,6LAAC;oCAAE,WAAU;8CACV;;;;;;;;;;;;sCAKL,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAO,WAAU;;sDAChB,6LAAC,2NAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;wCACxB;wCAAS;;;;;;;8CAGZ,6LAAC;oCAAO,WAAU;;sDAChB,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAoC;;;;;;;8CAIvD,6LAAC;oCACC,SAAS,IAAM,WAAW,CAAC;oCAC3B,WAAW,AAAC,2EAEX,OADC,UAAU,kCAAkC;;sDAG9C,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAW,AAAC,mCAAgE,OAA9B,UAAU,iBAAiB;;;;;;wCAClF,UAAU,UAAU;;;;;;;8CAGvB,6LAAC;oCAAO,WAAU;8CAChB,cAAA,6LAAC,mNAAA,CAAA,iBAAc;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxC;GA9JwB;KAAA", "debugId": null}}, {"offset": {"line": 324, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/arrow-up.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Project/reddit-clone/node_modules/lucide-react/src/icons/arrow-up.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm5 12 7-7 7 7', key: 'hav0vg' }],\n  ['path', { d: 'M12 19V5', key: 'x0mq9r' }],\n];\n\n/**\n * @component @name ArrowUp\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtNSAxMiA3LTcgNyA3IiAvPgogIDxwYXRoIGQ9Ik0xMiAxOVY1IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/arrow-up\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowUp = createLucideIcon('arrow-up', __iconNode);\n\nexport default ArrowUp;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC9C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAU,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 366, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/arrow-down.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Project/reddit-clone/node_modules/lucide-react/src/icons/arrow-down.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 5v14', key: 's699le' }],\n  ['path', { d: 'm19 12-7 7-7-7', key: '1idqje' }],\n];\n\n/**\n * @component @name ArrowDown\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgNXYxNCIgLz4KICA8cGF0aCBkPSJtMTkgMTItNyA3LTctNyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/arrow-down\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowDown = createLucideIcon('arrow-down', __iconNode);\n\nexport default ArrowDown;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACjD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAY,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAc,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 408, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/message-circle.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Project/reddit-clone/node_modules/lucide-react/src/icons/message-circle.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M7.9 20A9 9 0 1 0 4 16.1L2 22Z', key: 'vv11sd' }],\n];\n\n/**\n * @component @name MessageCircle\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNy45IDIwQTkgOSAwIDEgMCA0IDE2LjFMMiAyMloiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/message-circle\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MessageCircle = createLucideIcon('message-circle', __iconNode);\n\nexport default MessageCircle;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkC,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACjE;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAgB,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,AAAjB,CAAA,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAkB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 443, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/share.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Project/reddit-clone/node_modules/lucide-react/src/icons/share.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 2v13', key: '1km8f5' }],\n  ['path', { d: 'm16 6-4-4-4 4', key: '13yo43' }],\n  ['path', { d: 'M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8', key: '1b2hhj' }],\n];\n\n/**\n * @component @name Share\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMnYxMyIgLz4KICA8cGF0aCBkPSJtMTYgNi00LTQtNCA0IiAvPgogIDxwYXRoIGQ9Ik00IDEydjhhMiAyIDAgMCAwIDIgMmgxMmEyIDIgMCAwIDAgMi0ydi04IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/share\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Share = createLucideIcon('share', __iconNode);\n\nexport default Share;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC9C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6C,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC5E;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 492, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/ellipsis.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Project/reddit-clone/node_modules/lucide-react/src/icons/ellipsis.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '1', key: '41hilf' }],\n  ['circle', { cx: '19', cy: '12', r: '1', key: '1wjl8i' }],\n  ['circle', { cx: '5', cy: '12', r: '1', key: '1pcz8c' }],\n];\n\n/**\n * @component @name Ellipsis\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxIiAvPgogIDxjaXJjbGUgY3g9IjE5IiBjeT0iMTIiIHI9IjEiIC8+CiAgPGNpcmNsZSBjeD0iNSIgY3k9IjEyIiByPSIxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/ellipsis\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Ellipsis = createLucideIcon('ellipsis', __iconNode);\n\nexport default Ellipsis;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACxD;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACxD;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,GAAA,CAAK;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAK,CAAA,CAAA,CAAA,CAAA,QAAA;QAAU,CAAA;KAAA;CACzD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAiB,AAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}]}